import { useEffect, useRef, useState, useCallback } from "react";
import { useGS<PERSON> } from '@/hooks/useGSAP';
import { useErrorHandler } from '@/components/ErrorBoundary';
import { getAccessibilityConfig } from '@/utils';
import {
  PROJECTS,
  ANIMATION_DURATIONS,
  ANIMATION_EASINGS,
  COLOR_VARIANTS
} from '@/constants';

// Video component with intersection observer for autoplay
interface VideoPlayerProps {
  src: string;
  poster?: string;
  className?: string;
  onLoadStart?: () => void;
  onLoadedData?: () => void;
  onError?: (error: React.SyntheticEvent<HTMLVideoElement, Event>) => void;
  onClick?: () => void;
}

function VideoPlayer({ src, poster, className, onLoadStart, onLoadedData, onError, onClick }: VideoPlayerProps) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [isInView, setIsInView] = useState(false);

  // Intersection Observer for autoplay/pause
  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const observer = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        setIsInView(entry.isIntersecting);

        if (entry.isIntersecting && entry.intersectionRatio > 0.5) {
          // Video is more than 50% visible, start playing
          video.play().catch((error) => {
            console.warn('Video autoplay failed:', error);
          });
        } else {
          // Video is not visible or less than 50% visible, pause
          video.pause();
        }
      },
      {
        threshold: [0, 0.5, 1],
        rootMargin: '0px 0px -10% 0px' // Start playing slightly before fully visible
      }
    );

    observer.observe(video);

    return () => {
      observer.disconnect();
      video.pause();
    };
  }, []);

  const handleLoadStart = useCallback(() => {
    setIsLoading(true);
    setHasError(false);
    onLoadStart?.();
  }, [onLoadStart]);

  const handleLoadedData = useCallback(() => {
    setIsLoading(false);
    onLoadedData?.();
  }, [onLoadedData]);

  const handleError = useCallback((event: React.SyntheticEvent<HTMLVideoElement, Event>) => {
    setIsLoading(false);
    setHasError(true);
    onError?.(event);
  }, [onError]);

  return (
    <div 
      className="relative w-full h-full overflow-hidden cursor-pointer"
      onClick={onClick}
    >
      {/* Loading state */}
      {isLoading && (
        <div className="absolute inset-0 bg-[#1A1A1A] flex items-center justify-center z-10">
          <div className="flex flex-col items-center space-y-3">
            <div className="w-8 h-8 border-2 border-[#FF3366] border-t-transparent rounded-full animate-spin"></div>
            <span className="text-gray-400 text-sm">Loading video...</span>
          </div>
        </div>
      )}

      {/* Error state */}
      {hasError && (
        <div className="absolute inset-0 bg-[#1A1A1A] flex items-center justify-center z-10">
          <div className="flex flex-col items-center space-y-3 text-center p-4">
            <div className="w-12 h-12 rounded-full bg-red-500/20 flex items-center justify-center">
              <span className="text-red-400 text-xl">⚠</span>
            </div>
            <span className="text-gray-400 text-sm">Video unavailable</span>
          </div>
        </div>
      )}

      {/* Video element */}
      <video
        ref={videoRef}
        src={src}
        poster={poster}
        className={`w-full h-full object-cover transition-opacity duration-500 ${
          isLoading || hasError ? 'opacity-0' : 'opacity-100'
        } ${className || ''}`}
        muted
        loop
        playsInline
        preload="metadata"
        onLoadStart={handleLoadStart}
        onLoadedData={handleLoadedData}
        onError={handleError}
        style={{
          willChange: isInView ? 'auto' : 'transform'
        }}
      />

      {/* Video overlay gradient */}
      <div className="absolute inset-0 bg-gradient-to-t from-[#0F0F0F] via-transparent to-transparent opacity-60 pointer-events-none" />
    </div>
  );
}

// Video Modal Component
interface VideoModalProps {
  isOpen: boolean;
  onClose: () => void;
  videoSrc: string;
  title: string;
}

function VideoModal({ isOpen, onClose, videoSrc, title }: VideoModalProps) {
  const modalRef = useRef<HTMLDivElement>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isVideoLoading, setIsVideoLoading] = useState(true);

  // Handle escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  // Focus management
  useEffect(() => {
    if (isOpen && modalRef.current) {
      modalRef.current.focus();
    }
  }, [isOpen]);

  // Handle video playback
  useEffect(() => {
    if (isOpen && videoRef.current) {
      videoRef.current.play().catch(console.warn);
    }
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <div 
      className="fixed inset-0 bg-black/90 backdrop-blur-sm flex items-center justify-center p-4 z-50"
      onClick={onClose}
    >
      <div 
        ref={modalRef}
        className="relative w-full max-w-6xl max-h-[90vh] bg-[#0F0F0F] rounded-lg overflow-hidden border border-gray-800 focus:outline-none"
        onClick={(e) => e.stopPropagation()}
        tabIndex={-1}
        role="dialog"
        aria-modal="true"
        aria-labelledby="modal-title"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-800">
          <h3 id="modal-title" className="text-xl font-bold text-[#F5F5F5]">
            {title}
          </h3>
          <button
            onClick={onClose}
            className="w-8 h-8 rounded-full bg-gray-800 hover:bg-gray-700 flex items-center justify-center text-gray-400 hover:text-white transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-[#FF3366]"
            aria-label="Close modal"
          >
            <span className="text-lg">×</span>
          </button>
        </div>

        {/* Video Container */}
        <div className="relative aspect-video bg-black">
          {isVideoLoading && (
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="flex flex-col items-center space-y-3">
                <div className="w-12 h-12 border-2 border-[#FF3366] border-t-transparent rounded-full animate-spin"></div>
                <span className="text-gray-400">Loading expanded video...</span>
              </div>
            </div>
          )}
          
          <video
            ref={videoRef}
            src={videoSrc}
            className="w-full h-full object-contain"
            controls
            muted
            loop
            playsInline
            onLoadedData={() => setIsVideoLoading(false)}
            onLoadStart={() => setIsVideoLoading(true)}
          />
        </div>
      </div>
    </div>
  );
}

export default function PortfolioSection() {
  const sectionRef = useRef<HTMLElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const cardsRef = useRef<HTMLDivElement[]>([]);
  const { gsap, animate, isAvailable } = useGSAP();
  const { handleError } = useErrorHandler();
  
  // Modal state
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedVideo, setSelectedVideo] = useState<{ src: string; title: string } | null>(null);

  useEffect(() => {
    if (isAvailable && sectionRef.current) {
      initializeAnimations();
    }
  }, [isAvailable]);

  const initializeAnimations = () => {
    try {
      if (!gsap || !titleRef.current) return;

      const accessibility = getAccessibilityConfig();

      if (accessibility.reduceMotion) {
        // Set final states immediately for reduced motion
        if (titleRef.current) {
          gsap.set(titleRef.current, { opacity: 1, y: 0 });
        }
        cardsRef.current.forEach((card) => {
          if (card) {
            gsap.set(card, { opacity: 1, y: 0, scale: 1 });
          }
        });
        return;
      }

      // Animate section title with scroll trigger
      gsap.fromTo(titleRef.current,
        { opacity: 0, y: 50 },
        {
          opacity: 1,
          y: 0,
          duration: ANIMATION_DURATIONS.SLOW,
          ease: ANIMATION_EASINGS.POWER2_OUT,
          scrollTrigger: {
            trigger: titleRef.current,
            start: "top 80%",
            end: "bottom 20%",
            toggleActions: "play none none reverse"
          }
        }
      );

      // Animate project cards with scroll triggers
      cardsRef.current.forEach((card, index) => {
        if (card) {
          gsap.fromTo(card,
            { 
              opacity: 0, 
              y: 80, 
              scale: 0.9 
            },
            {
              opacity: 1,
              y: 0,
              scale: 1,
              duration: ANIMATION_DURATIONS.SLOW,
              delay: index * 0.2,
              ease: ANIMATION_EASINGS.POWER2_OUT,
              scrollTrigger: {
                trigger: card,
                start: "top 85%",
                end: "bottom 20%",
                toggleActions: "play none none reverse"
              }
            }
          );
        }
      });
    } catch (error) {
      handleError(error as Error);
    }
  };

  const handleCardHover = (index: number, isEntering: boolean) => {
    const card = cardsRef.current[index];
    if (!card || !gsap || !isAvailable) return;

    try {
      const accessibility = getAccessibilityConfig();
      
      if (accessibility.reduceMotion) {
        // Subtle changes for reduced motion
        if (isEntering) {
          gsap.to(card, {
            scale: 1.01,
            duration: ANIMATION_DURATIONS.FAST,
            ease: ANIMATION_EASINGS.POWER2_OUT
          });
        } else {
          gsap.to(card, {
            scale: 1,
            duration: ANIMATION_DURATIONS.FAST,
            ease: ANIMATION_EASINGS.POWER2_OUT
          });
        }
        return;
      }

      if (isEntering) {
        gsap.to(card, {
          y: -10,
          scale: 1.02,
          boxShadow: `0 20px 40px rgba(255, 51, 102, 0.3), 0 0 30px rgba(74, 144, 226, 0.2)`,
          duration: ANIMATION_DURATIONS.NORMAL,
          ease: ANIMATION_EASINGS.POWER2_OUT
        });
      } else {
        gsap.to(card, {
          y: 0,
          scale: 1,
          boxShadow: "0 10px 30px rgba(0, 0, 0, 0.3)",
          duration: ANIMATION_DURATIONS.NORMAL,
          ease: ANIMATION_EASINGS.POWER2_OUT
        });
      }
    } catch (error) {
      handleError(error as Error);
    }
  };

  const handleVideoClick = (videoSrc: string, title: string) => {
    setSelectedVideo({ src: videoSrc, title });
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedVideo(null);
  };

  return (
    <section 
      ref={sectionRef}
      className="min-h-screen bg-[#1A1A1A] py-20 px-4 relative"
      id="portfolio"
    >
      {/* Background gradient */}
      <div className="absolute inset-0 bg-gradient-radial opacity-50" />
      
      <div className="max-w-7xl mx-auto relative z-10">
        {/* Section Title */}
        <div className="text-center mb-16">
          <h2 
            ref={titleRef}
            className="text-4xl md:text-5xl lg:text-6xl font-black text-[#F5F5F5] mb-6 tracking-wide"
          >
            FEATURED WORK
          </h2>
          <p className="text-lg md:text-xl font-light tracking-widest text-gray-400 uppercase">
            Creative Solutions & Digital Experiences
          </p>
        </div>

        {/* Project Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 lg:gap-12">
          {PROJECTS.map((project, index) => (
            <div
              key={project.id}
              ref={(el) => {
                if (el) cardsRef.current[index] = el;
              }}
              className="group relative bg-[#0F0F0F] rounded-lg overflow-hidden border border-gray-800"
              style={{
                boxShadow: "0 10px 30px rgba(0, 0, 0, 0.3)",
                willChange: "transform"
              }}
              onMouseEnter={() => handleCardHover(index, true)}
              onMouseLeave={() => handleCardHover(index, false)}
            >
              {/* Badges - Only show for non-video cards (index !== 0) */}
              {index !== 0 && (
                <div className="absolute inset-0 flex items-center justify-center z-20">
                  {/* Coming Soon Badge - Centered in image area */}
                  <div className="bg-gradient-to-r from-[#FF3366] to-[#4A90E2] text-white px-6 py-3 rounded-full shadow-2xl">
                    <span className="text-base md:text-lg font-bold uppercase tracking-wider">
                      Coming Soon
                    </span>
                  </div>
                </div>
              )}

              {/* Project Image/Video */}
              <div className="relative h-64 overflow-hidden">
                {index === 0 ? (
                  // First project gets video without play button overlay
                  <VideoPlayer
                    src="/portfolio-video-prototype.mp4"
                    poster={project.placeholder}
                    className="transition-transform duration-500 group-hover:scale-110"
                    onLoadStart={() => console.log('Video loading started')}
                    onLoadedData={() => console.log('Video loaded successfully')}
                    onError={(error) => {
                      console.error('Video failed to load:', error);
                      handleError(new Error('Video playback failed'));
                    }}
                    onClick={() => handleVideoClick('/portfolio-video-prototype.mp4', project.title)}
                  />
                ) : (
                  // Other projects use placeholder images
                  <div className="relative w-full h-full">
                    <img
                      src={project.placeholder}
                      alt={project.title}
                      className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                      onError={(e) => {
                        // Hide broken images gracefully
                        const target = e.target as HTMLImageElement;
                        target.style.display = 'none';
                      }}
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-[#0F0F0F] via-transparent to-transparent opacity-60" />
                  </div>
                )}
              </div>

              {/* Project Content */}
              <div className="p-6">
                <div className="mb-3">
                  <span className="text-[#4A90E2] text-sm font-medium uppercase tracking-wider">
                    {project.type}
                  </span>
                </div>
                
                <h3 className="text-xl md:text-2xl font-bold text-[#F5F5F5] mb-3 group-hover:text-[#FF3366] transition-colors duration-300">
                  {project.title}
                </h3>
                
                <p className="text-gray-400 leading-relaxed mb-4">
                  {project.description}
                </p>

                {/* Action Buttons */}
                <div className="flex space-x-3">
                  <button className="px-4 py-2 bg-[#FF3366] text-white rounded-md text-sm font-medium hover:bg-[#E02A56] transition-colors duration-300 opacity-50 cursor-not-allowed">
                    View Project
                  </button>
                  <button className="px-4 py-2 border border-[#4A90E2] text-[#4A90E2] rounded-md text-sm font-medium hover:bg-[#4A90E2] hover:text-white transition-all duration-300 opacity-50 cursor-not-allowed">
                    Live Demo
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16">
          <p className="text-gray-400 mb-6">
            More projects coming soon. Stay tuned for updates.
          </p>
          <div className="inline-flex items-center space-x-2 text-[#FF3366]">
            <span className="w-2 h-2 bg-[#FF3366] rounded-full animate-pulse"></span>
            <span className="text-sm font-medium uppercase tracking-wider">
              Currently in development
            </span>
          </div>
        </div>
      </div>

      {/* Video Modal */}
      {selectedVideo && (
        <VideoModal
          isOpen={isModalOpen}
          onClose={handleCloseModal}
          videoSrc={selectedVideo.src}
          title={selectedVideo.title}
        />
      )}
    </section>
  );
}